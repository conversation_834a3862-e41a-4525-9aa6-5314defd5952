<script setup lang="ts">
import { ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import AppHeader from './components/AppHeader.vue'
import ToastNotification from './components/ToastNotification.vue'

const { locale } = useI18n()
const currentLocale = ref(locale.value)

const changeLocale = () => {
  locale.value = currentLocale.value
  localStorage.setItem('locale', currentLocale.value)
}

watch(locale, (newLocale) => {
  currentLocale.value = newLocale
})
</script>

<template>
  <div id="app" class="min-h-screen bg-gray-100">
    <AppHeader />
    <main class="pb-safe">
      <router-view v-slot="{ Component }">
        <Transition name="fade" mode="out-in">
          <component :is="Component" />
        </Transition>
      </router-view>
    </main>

    <!-- Language Switcher -->
    <div class="fixed bottom-4 right-4 z-50">
      <select
        v-model="currentLocale"
        @change="changeLocale"
        class="px-3 py-2 bg-white border border-gray-300 rounded-lg shadow-sm text-sm focus:outline-none focus:ring-2 focus:ring-primary"
      >
        <option value="en">English</option>
        <option value="zh">中文</option>
        <option value="hi">हिंदी</option>
        <option value="pt">Português</option>
      </select>
    </div>

    <!-- Toast Notifications -->
    <ToastNotification />
  </div>
</template>

<style>
.pb-safe {
  padding-bottom: env(safe-area-inset-bottom);
}

/* Page transition animations */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
