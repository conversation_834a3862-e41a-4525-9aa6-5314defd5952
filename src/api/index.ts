import axios from 'axios'

// Create axios instance with base configuration
const api = axios.create({
  baseURL: 'http://service.haiwailaba.cyou',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('authToken')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // Add user ID header if available
    const userId = localStorage.getItem('userId')
    if (userId) {
      config.headers['userid'] = userId
    }

    // Log API requests for debugging
    console.log('API Request:', config.method?.toUpperCase(), config.url, config.data)

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.config.url, response.data)
    return response.data
  },
  (error) => {
    // Handle common errors
    if (error.response?.status === 401) {
      // Handle unauthorized
      localStorage.removeItem('authToken')
      localStorage.removeItem('userId')
      // Could redirect to login if needed
    }

    // Log error for debugging
    console.error('API Error:', error.response?.data || error.message)

    return Promise.reject(error)
  }
)

// API endpoints based on the real API structure
export const walletAPI = {
  // User authentication
  login: (data: { phone: string; password: string }) =>
    api.post('/api/v1/auth/login', data),

  register: (data: { phone: string; password: string; code?: string }) =>
    api.post('/api/v1/auth/register', data),

  // Get wallet balance
  getBalance: (userId: string) =>
    api.get('/api/v1/wallet/balance', {
      params: { userId }
    }),

  // Get user info
  getUserInfo: (userId: string) =>
    api.get('/api/v1/user/info', {
      params: { userId }
    }),

  // Deposit
  deposit: (data: {
    userId: string;
    amount: number;
    paymentMethod: string;
    paymentChannel: string
  }) => api.post('/api/v1/wallet/deposit', data),

  // Withdraw
  withdraw: (data: {
    userId: string;
    amount: number;
    bankAccountId: string
  }) => api.post('/api/v1/wallet/withdraw', data),

  // Get transactions
  getTransactions: (userId: string, params?: {
    type?: string;
    status?: string;
    page?: number;
    limit?: number;
  }) => api.get('/api/v1/wallet/transactions', {
    params: { userId, ...params }
  }),

  // Payment methods
  getPaymentMethods: (userId: string) =>
    api.get('/api/v1/payment/methods', {
      params: { userId }
    }),

  getPaymentChannels: (paymentMethod: string) =>
    api.get('/api/v1/payment/channels', {
      params: { method: paymentMethod }
    }),

  // Bank account management
  getBankAccounts: (userId: string) =>
    api.get('/api/v1/bank/accounts', {
      params: { userId }
    }),

  addBankAccount: (data: {
    userId: string;
    accountNumber: string;
    ifscCode: string;
    accountName: string;
    bankProof?: string; // Base64 encoded image
  }) => api.post('/api/v1/bank/account/add', data),

  verifyBankAccount: (data: {
    userId: string;
    accountId: string;
    otp?: string;
  }) => api.post('/api/v1/bank/account/verify', data),

  // Credit card management
  addCreditCard: (data: {
    userId: string;
    cardNumber: string;
    cardholderName: string;
    expiryDate: string;
    cvv: string;
    saveCard: boolean;
  }) => api.post('/api/v1/payment/card/add', data),

  // UPI management
  addUPI: (data: {
    userId: string;
    upiId: string;
    provider: string
  }) => api.post('/api/v1/payment/upi/add', data),

  // File upload
  uploadFile: (formData: FormData) =>
    api.post('/api/v1/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }),

  // Get deposit/withdraw limits
  getLimits: (userId: string) =>
    api.get('/api/v1/wallet/limits', {
      params: { userId }
    }),

  // Apply bonus code
  applyBonusCode: (data: {
    userId: string;
    code: string;
  }) => api.post('/api/v1/bonus/apply', data),

  // Get notifications
  getNotifications: (userId: string) =>
    api.get('/api/v1/notifications', {
      params: { userId }
    })
}

export default api
