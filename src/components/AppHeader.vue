<template>
  <div class="fixed top-0 left-0 right-0 z-50 bg-primary text-white">
    <div class="flex items-center justify-between h-14 px-4">
      <!-- Left Button -->
      <div class="w-14 flex items-center">
        <button
          v-if="showBack"
          @click="$router.back()"
          class="p-2 hover:bg-white/10 rounded-lg transition-colors"
        >
          <ChevronLeft class="w-5 h-5" />
        </button>
      </div>

      <!-- Title -->
      <h1 class="text-lg font-medium flex-1 text-center">{{ title }}</h1>

      <!-- Right Button -->
      <div class="w-20 flex items-center justify-end">
        <button
          @click="handleChatClick"
          class="p-2 hover:bg-white/10 rounded-lg transition-colors"
        >
          <img :src="chatIcon" alt="chat" class="w-9 h-9" >
        </button>
      </div>
    </div>
  </div>
  <div class="h-14"/>
</template>

<script setup lang="ts">
import { ChevronLeft } from 'lucide-vue-next'
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useToastStore } from '@/stores/toast'
import chatIcon from '@/assets/images/chat2.png'

const route = useRoute()
const toast = useToastStore()

const title = computed(() => route.meta.title || 'PaySMS Wallet')
const showBack = computed(() => route.path !== '/')

const handleChatClick = () => {
  toast.info('Customer support feature coming soon!')
}
</script>
