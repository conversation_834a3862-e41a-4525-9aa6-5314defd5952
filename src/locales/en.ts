export default {
  common: {
    currency: '₹',
    cancel: 'Cancel',
    ok: 'OK',
    add: 'Add',
    link: 'Link',
    copy: 'Copy'
  },
  balance: {
    myBalance: 'My Balance',
    totalBalance: 'TOTAL BALANCE',
    addCash: 'ADD CASH',
    amountAdded: 'AMOUNT ADDED(UNUTILISED)',
    winnings: 'WINNINGS',
    cashBonus: 'CASH BONUS',
    withdrawInstantly: 'WITHDRAW INSTANTLY',
    myTransactions: 'My Transactions',
    managePayments: 'Manage Payments',
    managePaymentsSubtitle: 'Add/Remove Cards,Wallets,etc.',
    bonusPlaceholder: 'Maximum usable Cash Bonus per match=10% of Entry Fees',
    tooltip: {
      amountAdded: 'Money added by you that you can use to join contest but can\'t withdraw',
      winnings: 'Money that you can withdraw or re-use to join any game',
      cashBonus: 'Money that you can convert to balance by spec scale'
    },
    maxUsableCashBonus: 'Maximum usable Cash Bonus per match=10% of Entry Fees.',
    knowMore: 'Know more'
  },
  deposit: {
    deposit: 'Deposit',
    currentBalance: 'Current Balance',
    paymentMethod: 'Payment Method',
    paymentChannel: 'Payment Channel',
    depositNow: 'DEPOSIT NOW',
    cashBalance: 'Cash Balance',
    cashBonus: 'Cash Bonus',
    depositLimit: 'Deposit min :₹{min} & max:₹{max} allowed each time',
    tips: 'Tips: {tips}'
  },
  withdraw: {
    withdraw: 'WITHDRAW',
    withdrawableBalance: 'Withdrawable Balance',
    addNewBankAccount: 'Add New Bank Account',
    addNewBankAccountDesc: 'Add a new bank account to withdraw to.',
    amount: 'Amount',
    withdrawNow: 'WITHDRAW NOW',
    withdrawLimit: 'Withdraw min :₹{min} & max:₹{max} allowed each time'
  },
  transactions: {
    myTransactions: 'My Transactions',
    deposits: 'Deposits',
    withdrawals: 'Withdrawals',
    bet: 'Bet',
    bonus: 'Bonus',
    success: 'Success',
    inprocess: 'In-process',
    failed: 'Failed',
    refund: 'Refund',
    win: 'Win',
    loss: 'Loss',
    transferin: 'Transfer in',
    transferout: 'Transfer Out',
    depositCreditedInfo: 'Deposit is usually credited in minutes',
    withdrawProcessedInfo: 'Withdraw is usually processed in minutes',
    bettingRecordsInfo: 'All your betting records are displayed here',
    bonusRecordsInfo: 'All your bonus records are displayed here',
    noTransactions: 'You\'ve not done any transactions till now.',
    needHelp: 'Need help with this order? Tap to contact us.'
  },
  payments: {
    managePayments: 'Manage Payments',
    myCreditCards: 'My Credit Cards',
    addNewCreditCard: 'Add New Credit Card',
    myBankAccounts: 'My Bank Accounts',
    verifyNewBankAccount: 'Verify New Bank Account',
    myWalletsUPI: 'My Wallets UPI ID',
    otherUPIID: 'Other UPI ID',
    enterValidUPI: 'Enter your valid UPI ID',
    upiPlaceholder: 'abc@okiccc',
    upiNote: 'UPI ID once added can\'t be changed later',
    proceed: 'Proceed'
  },
  bank: {
    addBankDetails: 'Add Bank Details',
    accountHolderName: 'Account Holder Name',
    accountNamePlaceholder: 'Enter account holder name',
    accountNumber: 'Account Number',
    accountNumberPlaceholder: 'Enter account number',
    reenterAccountNumber: 'Re-enter Account Number',
    reenterAccountNumberPlaceholder: 'Re-enter account number',
    ifscCode: 'IFSC Code',
    ifscCodePlaceholder: 'Enter IFSC code',
    bankName: 'Bank Name',
    bankProof: 'BANK PROOF',
    uploadBankProof: 'Upload Bank Proof',
    dragOrClick: 'Drag and drop or click to upload',
    important: 'IMPORTANT',
    reviewDetails: 'Review your details before submitting your documents permanently.',
    cannotChange: 'Bank account once added can\'t be changed later.',
    verifyAccount: 'Verify Account',
    submitDetails: 'SUBMIT DETAILS',
    verifying: 'Verifying...',
    submitting: 'Submitting...',
    accountAdded: 'Bank account added successfully',
    addFailed: 'Failed to add bank account. Please try again.',
    errors: {
      nameRequired: 'Account holder name is required',
      nameTooShort: 'Name must be at least 3 characters',
      accountRequired: 'Account number is required',
      accountInvalid: 'Please enter a valid account number (9-18 digits)',
      confirmAccountRequired: 'Please re-enter account number',
      accountMismatch: 'Account numbers do not match',
      ifscRequired: 'IFSC code is required',
      ifscInvalid: 'Please enter a valid IFSC code',
      invalidFileType: 'Please upload an image file',
      fileTooLarge: 'File size must be less than 5MB'
    }
  },
  card: {
    addCreditCard: 'Add Credit Card',
    cardNumber: 'Card Number',
    cardNumberPlaceholder: '1234 5678 9012 3456',
    cardholderName: 'Cardholder Name',
    cardholderNamePlaceholder: 'JOHN DOE',
    expiryDate: 'Expiry Date',
    cvv: 'CVV',
    cvvInfo: 'CVV is the 3 or 4 digit security code on the back of your card',
    saveCard: 'Save this card for future payments',
    important: 'IMPORTANT',
    securePayment: 'Your payment information is secure and encrypted',
    cardNotStored: 'We do not store your full card details',
    encryptedTransaction: 'All transactions are encrypted with bank-level security',
    addCard: 'ADD CARD',
    adding: 'Adding...',
    cardAdded: 'Credit card added successfully',
    addFailed: 'Failed to add credit card. Please try again.',
    errors: {
      cardNumberRequired: 'Card number is required',
      cardNumberInvalid: 'Please enter a valid card number',
      nameRequired: 'Cardholder name is required',
      nameInvalid: 'Please use only letters and spaces',
      expiryRequired: 'Expiry date is required',
      expiryInvalid: 'Please enter a valid expiry date (MM/YY)',
      cardExpired: 'This card has expired',
      cvvRequired: 'CVV is required',
      cvvInvalid: 'Please enter a valid CVV (3-4 digits)'
    }
  }
}
