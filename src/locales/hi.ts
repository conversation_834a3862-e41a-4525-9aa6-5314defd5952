export default {
  common: {
    currency: '₹',
    cancel: 'रद्द करें',
    ok: 'ठीक है',
    add: 'जोड़ें',
    link: 'लिंक',
    copy: 'कॉपी'
  },
  balance: {
    myBalance: 'मेरी शेष राशि',
    totalBalance: 'कुल शेष राशि',
    addCash: 'नकद जोड़ें',
    amountAdded: 'जोड़ी गई राशि (अप्रयुक्त)',
    winnings: 'जीतें',
    cashBonus: 'नकद बोनस',
    withdrawInstantly: 'तुरंत निकालें',
    myTransactions: 'मेरे लेन-देन',
    managePayments: 'भुगतान प्रबंधित करें',
    managePaymentsSubtitle: 'कार्ड, वॉलेट आदि जोड़ें/हटाएं।',
    tooltip: {
      amountAdded: 'आपके द्वारा जोड़ा गया पैसा जिसका उपयोग आप प्रतियोगिता में शामिल होने के लिए कर सकते हैं लेकिन निकाल नहीं सकते',
      winnings: 'वह पैसा जिसे आप निकाल सकते हैं या किसी भी गेम में शामिल होने के लिए फिर से उपयोग कर सकते हैं',
      cashBonus: 'वह पैसा जिसे आप विशेष पैमाने से शेष राशि में परिवर्तित कर सकते हैं'
    },
    maxUsableCashBonus: 'प्रति मैच अधिकतम उपयोग योग्य नकद बोनस = प्रवेश शुल्क का 10%।',
    knowMore: 'और जानें'
  },
  deposit: {
    deposit: 'जमा करें',
    currentBalance: 'वर्तमान शेष राशि',
    paymentMethod: 'भुगतान विधि',
    paymentChannel: 'भुगतान चैनल',
    depositNow: 'अभी जमा करें',
    cashBalance: 'नकद शेष',
    cashBonus: 'नकद बोनस',
    depositLimit: 'न्यूनतम जमा: ₹{min} और अधिकतम: ₹{max} प्रत्येक बार अनुमत',
    tips: 'सुझाव: {tips}'
  },
  withdraw: {
    withdraw: 'निकासी',
    withdrawableBalance: 'निकाली जा सकने वाली शेष राशि',
    addNewBankAccount: 'नया बैंक खाता जोड़ें',
    addNewBankAccountDesc: 'निकासी के लिए एक नया बैंक खाता जोड़ें।',
    amount: 'राशि',
    withdrawNow: 'अभी निकालें',
    withdrawLimit: 'न्यूनतम निकासी: ₹{min} और अधिकतम: ₹{max} प्रत्येक बार अनुमत'
  },
  transactions: {
    myTransactions: 'मेरे लेन-देन',
    deposits: 'जमा',
    withdrawals: 'निकासी',
    bet: 'दांव',
    bonus: 'बोनस',
    success: 'सफल',
    inprocess: 'प्रक्रिया में',
    failed: 'असफल',
    refund: 'धनवापसी',
    win: 'जीत',
    loss: 'हार',
    transferin: 'ट्रांसफर इन',
    transferout: 'ट्रांसफर आउट',
    depositCreditedInfo: 'जमा आमतौर पर मिनटों में जमा हो जाता है',
    withdrawProcessedInfo: 'निकासी आमतौर पर मिनटों में संसाधित हो जाती है',
    bettingRecordsInfo: 'आपके सभी सट्टेबाजी रिकॉर्ड यहां प्रदर्शित हैं',
    bonusRecordsInfo: 'आपके सभी बोनस रिकॉर्ड यहां प्रदर्शित हैं',
    noTransactions: 'आपने अभी तक कोई लेन-देन नहीं किया है।',
    needHelp: 'इस ऑर्डर में मदद चाहिए? हमसे संपर्क करने के लिए टैप करें।'
  },
  payments: {
    managePayments: 'भुगतान प्रबंधित करें',
    myCreditCards: 'मेरे क्रेडिट कार्ड',
    addNewCreditCard: 'नया क्रेडिट कार्ड जोड़ें',
    myBankAccounts: 'मेरे बैंक खाते',
    verifyNewBankAccount: 'नए बैंक खाते को सत्यापित करें',
    myWalletsUPI: 'मेरे वॉलेट UPI ID',
    otherUPIID: 'अन्य UPI ID',
    enterValidUPI: 'अपना वैध UPI ID दर्ज करें',
    upiPlaceholder: 'abc@okiccc',
    upiNote: 'एक बार जोड़ा गया UPI ID बाद में नहीं बदला जा सकता',
    proceed: 'आगे बढ़ें'
  },
  bank: {
    addBankDetails: 'बैंक विवरण जोड़ें',
    accountHolderName: 'खाताधारक का नाम',
    accountNamePlaceholder: 'खाताधारक का नाम दर्ज करें',
    accountNumber: 'खाता संख्या',
    accountNumberPlaceholder: 'खाता संख्या दर्ज करें',
    reenterAccountNumber: 'खाता संख्या फिर से दर्ज करें',
    reenterAccountNumberPlaceholder: 'खाता संख्या फिर से दर्ज करें',
    ifscCode: 'IFSC कोड',
    ifscCodePlaceholder: 'IFSC कोड दर्ज करें',
    bankName: 'बैंक का नाम',
    bankProof: 'बैंक प्रमाण',
    uploadBankProof: 'बैंक प्रमाण अपलोड करें',
    dragOrClick: 'ड्रैग और ड्रॉप करें या अपलोड करने के लिए क्लिक करें',
    important: 'महत्वपूर्ण',
    reviewDetails: 'अपने दस्तावेज़ों को स्थायी रूप से जमा करने से पहले अपने विवरण की समीक्षा करें।',
    cannotChange: 'एक बार जोड़ा गया बैंक खाता बाद में नहीं बदला जा सकता।',
    verifyAccount: 'खाता सत्यापित करें',
    submitDetails: 'विवरण जमा करें',
    verifying: 'सत्यापित हो रहा है...',
    submitting: 'जमा हो रहा है...',
    accountAdded: 'बैंक खाता सफलतापूर्वक जोड़ा गया',
    addFailed: 'बैंक खाता जोड़ने में विफल। कृपया पुन: प्रयास करें।',
    errors: {
      nameRequired: 'खाताधारक का नाम आवश्यक है',
      nameTooShort: 'नाम कम से कम 3 अक्षरों का होना चाहिए',
      accountRequired: 'खाता संख्या आवश्यक है',
      accountInvalid: 'कृपया एक मान्य खाता संख्या दर्ज करें (9-18 अंक)',
      confirmAccountRequired: 'कृपया खाता संख्या फिर से दर्ज करें',
      accountMismatch: 'खाता संख्या मेल नहीं खाती',
      ifscRequired: 'IFSC कोड आवश्यक है',
      ifscInvalid: 'कृपया एक मान्य IFSC कोड दर्ज करें',
      invalidFileType: 'कृपया एक छवि फ़ाइल अपलोड करें',
      fileTooLarge: 'फ़ाइल का आकार 5MB से कम होना चाहिए'
    }
  },
  card: {
    addCreditCard: 'क्रेडिट कार्ड जोड़ें',
    cardNumber: 'कार्ड नंबर',
    cardNumberPlaceholder: '1234 5678 9012 3456',
    cardholderName: 'कार्डधारक का नाम',
    cardholderNamePlaceholder: 'राज कुमार',
    expiryDate: 'समाप्ति तिथि',
    cvv: 'CVV',
    cvvInfo: 'CVV आपके कार्ड के पीछे का 3 या 4 अंकों का सुरक्षा कोड है',
    saveCard: 'भविष्य के भुगतान के लिए इस कार्ड को सहेजें',
    important: 'महत्वपूर्ण',
    securePayment: 'आपकी भुगतान जानकारी सुरक्षित और एन्क्रिप्टेड है',
    cardNotStored: 'हम आपके पूर्ण कार्ड विवरण संग्रहीत नहीं करते',
    encryptedTransaction: 'सभी लेनदेन बैंक-स्तरीय सुरक्षा के साथ एन्क्रिप्ट किए गए हैं',
    addCard: 'कार्ड जोड़ें',
    adding: 'जोड़ा जा रहा है...',
    cardAdded: 'क्रेडिट कार्ड सफलतापूर्वक जोड़ा गया',
    addFailed: 'क्रेडिट कार्ड जोड़ने में विफल। कृपया पुन: प्रयास करें।',
    errors: {
      cardNumberRequired: 'कार्ड नंबर आवश्यक है',
      cardNumberInvalid: 'कृपया एक मान्य कार्ड नंबर दर्ज करें',
      nameRequired: 'कार्डधारक का नाम आवश्यक है',
      nameInvalid: 'कृपया केवल अक्षरों और रिक्त स्थान का उपयोग करें',
      expiryRequired: 'समाप्ति तिथि आवश्यक है',
      expiryInvalid: 'कृपया एक मान्य समाप्ति तिथि दर्ज करें (MM/YY)',
      cardExpired: 'यह कार्ड समाप्त हो गया है',
      cvvRequired: 'CVV आवश्यक है',
      cvvInvalid: 'कृपया एक मान्य CVV दर्ज करें (3-4 अंक)'
    }
  }
}
