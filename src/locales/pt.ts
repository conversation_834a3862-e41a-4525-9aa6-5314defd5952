export default {
  common: {
    currency: '₹',
    cancel: 'Cancelar',
    ok: 'OK',
    add: 'Adicionar',
    link: 'Vincular',
    copy: 'Copiar'
  },
  balance: {
    myBalance: 'Meu Saldo',
    totalBalance: 'SALDO TOTAL',
    addCash: 'ADICIONAR DINHEIRO',
    amountAdded: 'VALOR ADICIONADO (NÃO UTILIZADO)',
    winnings: 'GANHOS',
    cashBonus: 'BÔNUS EM DINHEIRO',
    withdrawInstantly: 'SACAR INSTANTANEAMENTE',
    myTransactions: 'Minhas Transações',
    managePayments: 'Gerenciar Pagamentos',
    managePaymentsSubtitle: 'Adicionar/Remover Cartões, Carteiras, etc.',
    tooltip: {
      amountAdded: 'Dinheiro adicionado por você que pode usar para participar de concursos, mas não pode sacar',
      winnings: 'Dinheiro que você pode sacar ou reutilizar para participar de qualquer jogo',
      cashBonus: 'Dinheiro que você pode converter em saldo por escala específica'
    },
    maxUsableCashBonus: 'Bônus em dinheiro máximo utilizável por partida = 10% das taxas de entrada.',
    knowMore: 'Saiba mais'
  },
  deposit: {
    deposit: 'Depósito',
    currentBalance: 'Saldo Atual',
    paymentMethod: 'Método de Pagamento',
    paymentChannel: 'Canal de Pagamento',
    depositNow: 'DEPOSITAR AGORA',
    cashBalance: 'Saldo em Dinheiro',
    cashBonus: 'Bônus em Dinheiro',
    depositLimit: 'Depósito mín: ₹{min} e máx: ₹{max} permitido cada vez',
    tips: 'Dicas: {tips}'
  },
  withdraw: {
    withdraw: 'SACAR',
    withdrawableBalance: 'Saldo Disponível para Saque',
    addNewBankAccount: 'Adicionar Nova Conta Bancária',
    addNewBankAccountDesc: 'Adicione uma nova conta bancária para sacar.',
    amount: 'Valor',
    withdrawNow: 'SACAR AGORA',
    withdrawLimit: 'Saque mín: ₹{min} e máx: ₹{max} permitido cada vez'
  },
  transactions: {
    myTransactions: 'Minhas Transações',
    deposits: 'Depósitos',
    withdrawals: 'Saques',
    bet: 'Aposta',
    bonus: 'Bônus',
    success: 'Sucesso',
    inprocess: 'Em processo',
    failed: 'Falhou',
    refund: 'Reembolso',
    win: 'Vitória',
    loss: 'Perda',
    transferin: 'Transferência entrada',
    transferout: 'Transferência saída',
    depositCreditedInfo: 'O depósito geralmente é creditado em minutos',
    withdrawProcessedInfo: 'O saque geralmente é processado em minutos',
    bettingRecordsInfo: 'Todos os seus registros de apostas são exibidos aqui',
    bonusRecordsInfo: 'Todos os seus registros de bônus são exibidos aqui',
    noTransactions: 'Você ainda não fez nenhuma transação.',
    needHelp: 'Precisa de ajuda com este pedido? Toque para nos contatar.'
  },
  payments: {
    managePayments: 'Gerenciar Pagamentos',
    myCreditCards: 'Meus Cartões de Crédito',
    addNewCreditCard: 'Adicionar Novo Cartão de Crédito',
    myBankAccounts: 'Minhas Contas Bancárias',
    verifyNewBankAccount: 'Verificar Nova Conta Bancária',
    myWalletsUPI: 'Minhas Carteiras UPI ID',
    otherUPIID: 'Outro UPI ID',
    enterValidUPI: 'Digite seu UPI ID válido',
    upiPlaceholder: 'abc@okiccc',
    upiNote: 'O UPI ID uma vez adicionado não pode ser alterado posteriormente',
    proceed: 'Prosseguir'
  },
  bank: {
    addBankDetails: 'Adicionar Detalhes Bancários',
    accountHolderName: 'Nome do Titular da Conta',
    accountNamePlaceholder: 'Digite o nome do titular da conta',
    accountNumber: 'Número da Conta',
    accountNumberPlaceholder: 'Digite o número da conta',
    reenterAccountNumber: 'Digite novamente o Número da Conta',
    reenterAccountNumberPlaceholder: 'Digite novamente o número da conta',
    ifscCode: 'Código IFSC',
    ifscCodePlaceholder: 'Digite o código IFSC',
    bankName: 'Nome do Banco',
    bankProof: 'COMPROVANTE BANCÁRIO',
    uploadBankProof: 'Carregar Comprovante Bancário',
    dragOrClick: 'Arraste e solte ou clique para carregar',
    important: 'IMPORTANTE',
    reviewDetails: 'Revise seus detalhes antes de enviar seus documentos permanentemente.',
    cannotChange: 'Conta bancária uma vez adicionada não pode ser alterada posteriormente.',
    verifyAccount: 'Verificar Conta',
    submitDetails: 'ENVIAR DETALHES',
    verifying: 'Verificando...',
    submitting: 'Enviando...',
    accountAdded: 'Conta bancária adicionada com sucesso',
    addFailed: 'Falha ao adicionar conta bancária. Por favor, tente novamente.',
    errors: {
      nameRequired: 'Nome do titular da conta é obrigatório',
      nameTooShort: 'Nome deve ter pelo menos 3 caracteres',
      accountRequired: 'Número da conta é obrigatório',
      accountInvalid: 'Por favor, insira um número de conta válido (9-18 dígitos)',
      confirmAccountRequired: 'Por favor, digite novamente o número da conta',
      accountMismatch: 'Os números de conta não correspondem',
      ifscRequired: 'Código IFSC é obrigatório',
      ifscInvalid: 'Por favor, insira um código IFSC válido',
      invalidFileType: 'Por favor, carregue um arquivo de imagem',
      fileTooLarge: 'O tamanho do arquivo deve ser menor que 5MB'
    }
  },
  card: {
    addCreditCard: 'Adicionar Cartão de Crédito',
    cardNumber: 'Número do Cartão',
    cardNumberPlaceholder: '1234 5678 9012 3456',
    cardholderName: 'Nome do Titular',
    cardholderNamePlaceholder: 'JOÃO SILVA',
    expiryDate: 'Data de Validade',
    cvv: 'CVV',
    cvvInfo: 'CVV é o código de segurança de 3 ou 4 dígitos no verso do seu cartão',
    saveCard: 'Salvar este cartão para pagamentos futuros',
    important: 'IMPORTANTE',
    securePayment: 'Suas informações de pagamento são seguras e criptografadas',
    cardNotStored: 'Não armazenamos os detalhes completos do seu cartão',
    encryptedTransaction: 'Todas as transações são criptografadas com segurança bancária',
    addCard: 'ADICIONAR CARTÃO',
    adding: 'Adicionando...',
    cardAdded: 'Cartão de crédito adicionado com sucesso',
    addFailed: 'Falha ao adicionar cartão de crédito. Por favor, tente novamente.',
    errors: {
      cardNumberRequired: 'Número do cartão é obrigatório',
      cardNumberInvalid: 'Por favor, insira um número de cartão válido',
      nameRequired: 'Nome do titular é obrigatório',
      nameInvalid: 'Por favor, use apenas letras e espaços',
      expiryRequired: 'Data de validade é obrigatória',
      expiryInvalid: 'Por favor, insira uma data de validade válida (MM/AA)',
      cardExpired: 'Este cartão expirou',
      cvvRequired: 'CVV é obrigatório',
      cvvInvalid: 'Por favor, insira um CVV válido (3-4 dígitos)'
    }
  }
}
