export default {
  common: {
    currency: '₹',
    cancel: '取消',
    ok: '确定',
    add: '添加',
    link: '链接',
    copy: '复制'
  },
  balance: {
    myBalance: '我的余额',
    totalBalance: '总余额',
    addCash: '充值',
    amountAdded: '充值金额（未使用）',
    winnings: '奖金',
    cashBonus: '现金红利',
    withdrawInstantly: '立即提现',
    myTransactions: '我的交易记录',
    managePayments: '管理支付方式',
    managePaymentsSubtitle: '添加/删除银行卡、钱包等',
    bonusPlaceholder: '每场比赛最大可用现金红利=入场费的10%',
    tooltip: {
      amountAdded: '您充值的金额可用于参加比赛，但不能提现',
      winnings: '可以提现或重新用于参加任何游戏的金额',
      cashBonus: '可以按照特定比例转换为余额的金额'
    },
    maxUsableCashBonus: '每场比赛最大可用现金红利=入场费的10%。',
    knowMore: '了解更多'
  },
  deposit: {
    deposit: '充值',
    currentBalance: '当前余额',
    paymentMethod: '支付方式',
    paymentChannel: '支付渠道',
    depositNow: '立即充值',
    cashBalance: '现金余额',
    cashBonus: '现金红利',
    depositLimit: '充值最低：₹{min}，最高：₹{max}',
    tips: '提示：{tips}'
  },
  withdraw: {
    withdraw: '提现',
    withdrawableBalance: '可提现余额',
    addNewBankAccount: '添加新银行账户',
    addNewBankAccountDesc: '添加新的银行账户用于提现。',
    amount: '金额',
    withdrawNow: '立即提现',
    withdrawLimit: '提现最低：₹{min}，最高：₹{max}'
  },
  transactions: {
    myTransactions: '我的交易记录',
    deposits: '充值',
    withdrawals: '提现',
    bet: '投注',
    bonus: '红利',
    success: '成功',
    inprocess: '处理中',
    failed: '失败',
    refund: '退款',
    win: '赢',
    loss: '输',
    transferin: '转入',
    transferout: '转出',
    depositCreditedInfo: '充值通常在几分钟内到账',
    withdrawProcessedInfo: '提现通常在几分钟内处理',
    bettingRecordsInfo: '您的所有投注记录都显示在这里',
    bonusRecordsInfo: '您的所有红利记录都显示在这里',
    noTransactions: '您还没有任何交易记录。',
    needHelp: '需要帮助？点击联系我们。'
  },
  payments: {
    managePayments: '管理支付方式',
    myCreditCards: '我的信用卡',
    addNewCreditCard: '添加新信用卡',
    myBankAccounts: '我的银行账户',
    verifyNewBankAccount: '验证新银行账户',
    myWalletsUPI: '我的钱包 UPI ID',
    otherUPIID: '其他 UPI ID',
    enterValidUPI: '输入有效的 UPI ID',
    upiPlaceholder: 'abc@okiccc',
    upiNote: 'UPI ID 一旦添加将无法更改',
    proceed: '继续'
  },
  bank: {
    addBankDetails: '添加银行详情',
    accountHolderName: '账户持有人姓名',
    accountNamePlaceholder: '输入账户持有人姓名',
    accountNumber: '账号',
    accountNumberPlaceholder: '输入账号',
    reenterAccountNumber: '重新输入账号',
    reenterAccountNumberPlaceholder: '重新输入账号',
    ifscCode: 'IFSC 代码',
    ifscCodePlaceholder: '输入 IFSC 代码',
    bankName: '银行名称',
    bankProof: '银行证明',
    uploadBankProof: '上传银行证明',
    dragOrClick: '拖拽或点击上传',
    important: '重要提示',
    reviewDetails: '在永久提交文件之前请仔细核对您的信息。',
    cannotChange: '银行账户一旦添加将无法更改。',
    verifyAccount: '验证账户',
    submitDetails: '提交详情',
    verifying: '验证中...',
    submitting: '提交中...',
    accountAdded: '银行账户添加成功',
    addFailed: '添加银行账户失败。请重试。',
    errors: {
      nameRequired: '账户持有人姓名是必填项',
      nameTooShort: '姓名至少需要3个字符',
      accountRequired: '账号是必填项',
      accountInvalid: '请输入有效的账号（9-18位数字）',
      confirmAccountRequired: '请重新输入账号',
      accountMismatch: '账号不匹配',
      ifscRequired: 'IFSC 代码是必填项',
      ifscInvalid: '请输入有效的 IFSC 代码',
      invalidFileType: '请上传图片文件',
      fileTooLarge: '文件大小必须小于 5MB'
    }
  },
  card: {
    addCreditCard: '添加信用卡',
    cardNumber: '卡号',
    cardNumberPlaceholder: '1234 5678 9012 3456',
    cardholderName: '持卡人姓名',
    cardholderNamePlaceholder: 'ZHANG SAN',
    expiryDate: '有效期',
    cvv: 'CVV',
    cvvInfo: 'CVV是您卡片背面的3或4位安全码',
    saveCard: '保存此卡以便将来付款',
    important: '重要提示',
    securePayment: '您的支付信息是安全和加密的',
    cardNotStored: '我们不会存储您的完整卡片信息',
    encryptedTransaction: '所有交易都采用银行级安全加密',
    addCard: '添加卡片',
    adding: '添加中...',
    cardAdded: '信用卡添加成功',
    addFailed: '添加信用卡失败。请重试。',
    errors: {
      cardNumberRequired: '卡号是必填项',
      cardNumberInvalid: '请输入有效的卡号',
      nameRequired: '持卡人姓名是必填项',
      nameInvalid: '请只使用字母和空格',
      expiryRequired: '有效期是必填项',
      expiryInvalid: '请输入有效的有效期（MM/YY）',
      cardExpired: '此卡已过期',
      cvvRequired: 'CVV是必填项',
      cvvInvalid: '请输入有效的CVV（3-4位数字）'
    }
  }
}
