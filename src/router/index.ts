import { createRouter, createWebHashHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Balance',
    component: () => import('@/views/Balance.vue'),
    meta: { title: 'My Balance' }
  },
  {
    path: '/pages/deposit/add/add',
    name: 'Deposit',
    component: () => import('@/views/Deposit.vue'),
    meta: { title: 'Deposit' }
  },
  {
    path: '/pages/withdraw/index',
    name: 'Withdraw',
    component: () => import('@/views/Withdraw.vue'),
    meta: { title: 'Withdraw' }
  },
  {
    path: '/pages/transaction/transaction',
    name: 'Transactions',
    component: () => import('@/views/Transactions.vue'),
    meta: { title: 'My Transactions' }
  },
  {
    path: '/pages/deposit/managepayment/managepayment',
    name: 'ManagePayments',
    component: () => import('@/views/ManagePayments.vue'),
    meta: { title: 'Manage Payments' }
  },
  {
    path: '/pages/deposit/managepayment/addbank/addbank',
    name: 'AddBankAccount',
    component: () => import('@/views/AddBankAccount.vue'),
    meta: { title: 'Add Bank Account' }
  },
  {
    path: '/pages/deposit/managepayment/addcard/addcard',
    name: 'AddCreditCard',
    component: () => import('@/views/AddCreditCard.vue'),
    meta: { title: 'Add Credit Card' }
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

router.beforeEach((to, _, next) => {
  document.title = (to.meta.title as string) || 'PaySMS Wallet'
  next()
})

export default router
