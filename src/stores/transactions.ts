import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface Transaction {
  id: string
  type: 'deposit' | 'withdrawal' | 'bet' | 'bonus'
  status: 'success' | 'in-process' | 'failed' | 'refund' | 'win' | 'loss' | 'transferin' | 'transferout'
  amount: number
  paymentMethod: string
  timestamp: string
  orderId: string
}

export type TabType = 'deposits' | 'withdrawals' | 'bet' | 'bonus'
export type FilterType = 'success' | 'in-process' | 'failed' | 'refund' | 'win' | 'loss' | 'transferin' | 'transferout' | 'all'

export const useTransactionsStore = defineStore('transactions', () => {
  const transactions = ref<Transaction[]>([
    {
      id: '1',
      type: 'deposit',
      status: 'in-process',
      amount: 300,
      paymentMethod: 'AliPay',
      timestamp: '12:48 pm',
      orderId: '202507301248350198519953'
    },
    {
      id: '2',
      type: 'deposit',
      status: 'in-process',
      amount: 300,
      paymentMethod: 'upi',
      timestamp: '12:48 pm',
      orderId: '202507301248320156545054'
    },
    {
      id: '3',
      type: 'deposit',
      status: 'in-process',
      amount: 300,
      paymentMethod: 'AliPay',
      timestamp: '12:48 pm',
      orderId: '202507301248270151565256'
    },
    {
      id: '4',
      type: 'deposit',
      status: 'in-process',
      amount: 300,
      paymentMethod: 'AliPay',
      timestamp: '12:48 pm',
      orderId: '202507301248240148535148'
    }
  ])

  const activeTab = ref<TabType>('deposits')
  const activeFilter = ref<FilterType>('all')

  const addTransaction = (transaction: Omit<Transaction, 'id'>) => {
    transactions.value.unshift({
      ...transaction,
      id: Date.now().toString()
    })
  }

  const getFilteredTransactions = () => {
    return transactions.value.filter(t => {
      // Convert tab type to transaction type
      const tabType = activeTab.value === 'deposits' ? 'deposit' :
                     activeTab.value === 'withdrawals' ? 'withdrawal' :
                     activeTab.value
      const typeMatch = t.type === tabType

      // Handle special filters for bet and bonus tabs
      let statusMatch = true
      if (activeFilter.value !== 'all') {
        if (activeTab.value === 'bet') {
          statusMatch = activeFilter.value === 'win' || activeFilter.value === 'loss' ?
                       t.status === activeFilter.value : false
        } else if (activeTab.value === 'bonus') {
          statusMatch = activeFilter.value === 'transferin' || activeFilter.value === 'transferout' ?
                       t.status === activeFilter.value : false
        } else {
          statusMatch = t.status === activeFilter.value
        }
      }

      return typeMatch && statusMatch
    })
  }

  // Reset filter when changing tabs
  const setActiveTab = (tab: TabType) => {
    activeTab.value = tab
    activeFilter.value = 'all'
  }

  return {
    transactions,
    activeTab,
    activeFilter,
    addTransaction,
    getFilteredTransactions,
    setActiveTab
  }
})
