import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { walletAPI } from '@/api'
import { useToastStore } from './toast'

export const useWalletStore = defineStore('wallet', () => {
  // State
  const totalBalance = ref(10)
  const amountAdded = ref(10)
  const winnings = ref(0)
  const cashBonus = ref(0)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // User info
  const userId = ref(localStorage.getItem('userId') || '')
  const userInfo = ref<any>(null)

  // Selected payment options
  const selectedPaymentMethod = ref<string>('')
  const selectedPaymentChannel = ref<string>('')

  // Payment methods and channels
  const paymentMethods = ref<any[]>([])
  const paymentChannels = ref<any[]>([])

  // Getters
  const withdrawableBalance = computed(() => winnings.value)
  const cashBalance = computed(() => amountAdded.value + winnings.value)

  // Actions
  const updateBalance = (data: {
    total?: number
    added?: number
    winnings?: number
    bonus?: number
  }) => {
    if (data.total !== undefined) totalBalance.value = data.total
    if (data.added !== undefined) amountAdded.value = data.added
    if (data.winnings !== undefined) winnings.value = data.winnings
    if (data.bonus !== undefined) cashBonus.value = data.bonus
  }

  const fetchBalance = async () => {
    if (!userId.value) {
      // Set demo user ID if not logged in
      userId.value = 'demo-user'
      setUserId('demo-user')
    }

    isLoading.value = true
    error.value = null

    try {
      const response = await walletAPI.getBalance(userId.value)

      if (response && response.data) {
        updateBalance({
          total: response.data.totalBalance || response.data.total || 0,
          added: response.data.amountAdded || response.data.added || 0,
          winnings: response.data.winnings || 0,
          bonus: response.data.cashBonus || response.data.bonus || 0
        })
      } else if (response) {
        // Handle different response structure
        const data = response as any
        updateBalance({
          total: data.totalBalance || data.total || 0,
          added: data.amountAdded || data.added || 0,
          winnings: data.winnings || 0,
          bonus: data.cashBonus || data.bonus || 0
        })
      }

      return true
    } catch (err: any) {
      console.error('Balance fetch error:', err)

      // For now, keep the demo balance if API fails
      if (err.response?.status === 404 || err.code === 'ERR_NETWORK') {
        // Keep current balance values
        return true
      }

      error.value = err.response?.data?.message || 'Failed to fetch balance'
      const toast = useToastStore()
      toast.error('Failed to load balance. Using demo data.')
      return false
    } finally {
      isLoading.value = false
    }
  }

  const fetchPaymentMethods = async () => {
    if (!userId.value) return

    try {
      const response = await walletAPI.getPaymentMethods(userId.value)
      paymentMethods.value = response.data || []
    } catch (err) {
      console.error('Failed to fetch payment methods:', err)
    }
  }

  const fetchPaymentChannels = async (method: string) => {
    try {
      const response = await walletAPI.getPaymentChannels(method)
      paymentChannels.value = response.data || []
    } catch (err) {
      console.error('Failed to fetch payment channels:', err)
    }
  }

  const addDeposit = async (amount: number, bonus: number = 0) => {
    if (!userId.value) {
      const toast = useToastStore()
      toast.error('Please login to continue')
      return false
    }

    isLoading.value = true
    error.value = null

    try {
      const response = await walletAPI.deposit({
        userId: userId.value,
        amount,
        paymentMethod: selectedPaymentMethod.value,
        paymentChannel: selectedPaymentChannel.value
      })

      // Update balance after successful deposit
      if (response.data) {
        amountAdded.value += amount
        totalBalance.value += amount
        if (bonus > 0) {
          cashBonus.value += bonus
          totalBalance.value += bonus
        }
      }

      const toast = useToastStore()
      toast.success(`Successfully deposited ₹${amount}`)

      return true
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Deposit failed'
      const toast = useToastStore()
      toast.error(error.value || 'Deposit failed')
      return false
    } finally {
      isLoading.value = false
    }
  }

  const withdraw = async (amount: number) => {
    if (!userId.value) {
      const toast = useToastStore()
      toast.error('Please login to continue')
      return false
    }

    if (amount > winnings.value) {
      const toast = useToastStore()
      toast.error('Insufficient withdrawable balance')
      return false
    }

    isLoading.value = true
    error.value = null

    try {
      const response = await walletAPI.withdraw({
        userId: userId.value,
        amount,
        bankAccountId: 'default' // This should be selected by user
      })

      // Update balance after successful withdrawal
      if (response.data) {
        winnings.value -= amount
        totalBalance.value -= amount
      }

      const toast = useToastStore()
      toast.success(`Successfully withdrew ₹${amount}`)

      return true
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Withdrawal failed'
      const toast = useToastStore()
      toast.error(error.value || 'Withdrawal failed')
      return false
    } finally {
      isLoading.value = false
    }
  }

  const setUserId = (id: string) => {
    userId.value = id
    localStorage.setItem('userId', id)
  }

  const logout = () => {
    userId.value = ''
    userInfo.value = null
    localStorage.removeItem('userId')
    localStorage.removeItem('authToken')

    // Reset balances
    updateBalance({
      total: 0,
      added: 0,
      winnings: 0,
      bonus: 0
    })
  }

  return {
    // State
    totalBalance,
    amountAdded,
    winnings,
    cashBonus,
    isLoading,
    error,
    userId,
    userInfo,
    selectedPaymentMethod,
    selectedPaymentChannel,
    paymentMethods,
    paymentChannels,

    // Getters
    withdrawableBalance,
    cashBalance,

    // Actions
    updateBalance,
    fetchBalance,
    fetchPaymentMethods,
    fetchPaymentChannels,
    addDeposit,
    withdraw,
    setUserId,
    logout
  }
})
