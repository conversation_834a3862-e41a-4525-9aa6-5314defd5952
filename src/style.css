@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary-color: #0a7f75;
  --primary-hover: #096b62;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: var(--gray-100);
  color: var(--gray-900);
  min-height: 100vh;
}

#app {
  min-height: 100vh;
  background-color: var(--gray-100);
}

/* Custom button styles */
@layer components {
  .btn-primary {
    @apply bg-[#0a7f75] text-white hover:bg-[#096b62] transition-colors duration-200;
  }

  .btn-success {
    @apply bg-green-500 text-white hover:bg-green-600 transition-colors duration-200;
  }

  /* Custom card styles */
  .card {
    @apply bg-white rounded-lg shadow-sm;
  }
}

/* Remove input focus outline and add custom */
input:focus {
  outline: none;
  border-color: var(--primary-color);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-400);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-500);
}

/* Transition classes */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from,
.slide-up-leave-to {
  transform: translateY(20px);
  opacity: 0;
}
