export interface ValidationRule {
  validate: (value: any) => boolean
  message: string
}

export interface ValidationResult {
  isValid: boolean
  errors: string[]
}

export class Validator {
  private rules: ValidationRule[] = []

  required(message = 'This field is required'): Validator {
    this.rules.push({
      validate: (value) => value !== null && value !== undefined && value !== '' && value !== 0,
      message
    })
    return this
  }

  min(minValue: number, message?: string): Validator {
    this.rules.push({
      validate: (value) => Number(value) >= minValue,
      message: message || `Minimum value is ${minValue}`
    })
    return this
  }

  max(maxValue: number, message?: string): Validator {
    this.rules.push({
      validate: (value) => Number(value) <= maxValue,
      message: message || `Maximum value is ${maxValue}`
    })
    return this
  }

  between(min: number, max: number, message?: string): Validator {
    this.rules.push({
      validate: (value) => {
        const num = Number(value)
        return num >= min && num <= max
      },
      message: message || `Value must be between ${min} and ${max}`
    })
    return this
  }

  pattern(regex: RegExp, message = 'Invalid format'): Validator {
    this.rules.push({
      validate: (value) => regex.test(String(value)),
      message
    })
    return this
  }

  custom(validate: (value: any) => boolean, message: string): Validator {
    this.rules.push({ validate, message })
    return this
  }

  validate(value: any): ValidationResult {
    const errors: string[] = []

    for (const rule of this.rules) {
      if (!rule.validate(value)) {
        errors.push(rule.message)
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }
}

// Factory function for creating validators
export const createValidator = () => new Validator()

// Common validators
export const validators = {
  depositAmount: () => createValidator()
    .required('Please enter an amount')
    .min(100, 'Minimum deposit amount is ₹100')
    .max(1000000, 'Maximum deposit amount is ₹1,000,000'),

  withdrawAmount: (maxAmount: number) => createValidator()
    .required('Please enter an amount')
    .min(1, 'Minimum withdrawal amount is ₹1')
    .max(maxAmount, `Maximum withdrawal amount is ₹${maxAmount.toLocaleString()}`),

  upiId: () => createValidator()
    .required('Please enter UPI ID')
    .pattern(/^[a-zA-Z0-9._]+@[a-zA-Z0-9]+$/, 'Please enter a valid UPI ID')
}
