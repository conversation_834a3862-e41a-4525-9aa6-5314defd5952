<template>
  <div class="min-h-screen bg-gray-100">
    <!-- Form -->
    <div class="bg-white">
      <div class="px-4 py-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-6">{{ $t('card.addCreditCard') }}</h2>

        <!-- Card Number -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            {{ $t('card.cardNumber') }}
          </label>
          <div class="relative">
            <input
              v-model="formData.cardNumber"
              type="text"
              :placeholder="$t('card.cardNumberPlaceholder')"
              maxlength="19"
              class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              :class="{ 'border-red-500': errors.cardNumber }"
              @input="formatCardNumber"
              @blur="validateField('cardNumber')"
            />
            <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
              <img v-if="cardType" :src="getCardIcon()" :alt="cardType" class="h-8" />
            </div>
          </div>
          <p v-if="errors.cardNumber" class="mt-1 text-sm text-red-500">{{ errors.cardNumber }}</p>
        </div>

        <!-- Cardholder Name -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            {{ $t('card.cardholderName') }}
          </label>
          <input
            v-model="formData.cardholderName"
            type="text"
            :placeholder="$t('card.cardholderNamePlaceholder')"
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent uppercase"
            :class="{ 'border-red-500': errors.cardholderName }"
            @input="formData.cardholderName = formData.cardholderName.toUpperCase()"
            @blur="validateField('cardholderName')"
          />
          <p v-if="errors.cardholderName" class="mt-1 text-sm text-red-500">{{ errors.cardholderName }}</p>
        </div>

        <!-- Expiry Date and CVV -->
        <div class="grid grid-cols-2 gap-4 mb-6">
          <!-- Expiry Date -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              {{ $t('card.expiryDate') }}
            </label>
            <input
              v-model="formData.expiryDate"
              type="text"
              placeholder="MM/YY"
              maxlength="5"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              :class="{ 'border-red-500': errors.expiryDate }"
              @input="formatExpiryDate"
              @blur="validateField('expiryDate')"
            />
            <p v-if="errors.expiryDate" class="mt-1 text-sm text-red-500">{{ errors.expiryDate }}</p>
          </div>

          <!-- CVV -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              {{ $t('card.cvv') }}
              <button
                @click="showCVVInfo = !showCVVInfo"
                class="ml-1 text-gray-400 hover:text-gray-600"
              >
                <Info class="w-4 h-4 inline" />
              </button>
            </label>
            <input
              v-model="formData.cvv"
              type="password"
              placeholder="•••"
              maxlength="4"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              :class="{ 'border-red-500': errors.cvv }"
              @input="formData.cvv = formData.cvv.replace(/\D/g, '')"
              @blur="validateField('cvv')"
            />
            <p v-if="errors.cvv" class="mt-1 text-sm text-red-500">{{ errors.cvv }}</p>
          </div>
        </div>

        <!-- CVV Info -->
        <Transition name="slide-down">
          <div v-if="showCVVInfo" class="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p class="text-sm text-blue-800">{{ $t('card.cvvInfo') }}</p>
          </div>
        </Transition>

        <!-- Save Card Option -->
        <div class="mb-6">
          <label class="flex items-center cursor-pointer">
            <input
              v-model="formData.saveCard"
              type="checkbox"
              class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary"
            />
            <span class="ml-2 text-sm text-gray-700">{{ $t('card.saveCard') }}</span>
          </label>
        </div>

        <!-- Important Notes -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-20">
          <h4 class="text-sm font-semibold text-gray-800 mb-2">{{ $t('card.important') }}</h4>
          <ul class="text-sm text-gray-700 space-y-1 list-disc list-inside">
            <li>{{ $t('card.securePayment') }}</li>
            <li>{{ $t('card.cardNotStored') }}</li>
            <li>{{ $t('card.encryptedTransaction') }}</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Submit Button -->
    <div class="fixed bottom-0 left-0 right-0 bg-white shadow-lg p-4">
      <button
        @click="handleSubmit"
        :disabled="!isFormValid || isLoading"
        :class="[
          'w-full py-4 rounded-md font-medium transition-all transform',
          isFormValid && !isLoading
            ? 'bg-primary text-white hover:bg-primary-hover hover:scale-105 active:scale-95'
            : 'bg-gray-300 text-gray-500 cursor-not-allowed'
        ]"
      >
        <span v-if="!isLoading">{{ $t('card.addCard') }}</span>
        <span v-else class="flex items-center justify-center">
          <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
          {{ $t('card.adding') }}
        </span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useToastStore } from '@/stores/toast'
import { useI18n } from 'vue-i18n'
import { Info } from 'lucide-vue-next'
import { walletAPI } from '@/api'
import visaIcon from '@/assets/images/visa-card.png'
import mastercardIcon from '@/assets/images/mastercard.png'

const router = useRouter()
const toast = useToastStore()
const { t } = useI18n()

const formData = reactive({
  cardNumber: '',
  cardholderName: '',
  expiryDate: '',
  cvv: '',
  saveCard: true
})

const errors = reactive({
  cardNumber: '',
  cardholderName: '',
  expiryDate: '',
  cvv: ''
})

const isLoading = ref(false)
const showCVVInfo = ref(false)
const cardType = ref<'visa' | 'mastercard' | null>(null)

const formatCardNumber = () => {
  // Remove all non-digits
  let value = formData.cardNumber.replace(/\s/g, '')

  // Detect card type
  if (value.startsWith('4')) {
    cardType.value = 'visa'
  } else if (value.match(/^5[1-5]/)) {
    cardType.value = 'mastercard'
  } else {
    cardType.value = null
  }

  // Format with spaces every 4 digits
  let formatted = ''
  for (let i = 0; i < value.length; i++) {
    if (i > 0 && i % 4 === 0) {
      formatted += ' '
    }
    formatted += value[i]
  }

  formData.cardNumber = formatted
}

const formatExpiryDate = () => {
  let value = formData.expiryDate.replace(/\D/g, '')

  if (value.length >= 2) {
    value = value.slice(0, 2) + '/' + value.slice(2, 4)
  }

  formData.expiryDate = value
}

const getCardIcon = () => {
  if (cardType.value === 'visa') return visaIcon
  if (cardType.value === 'mastercard') return mastercardIcon
  return ''
}

const validateField = (field: keyof typeof formData) => {
  errors[field as keyof typeof errors] = ''

  switch (field) {
    case 'cardNumber': {
      const cardNum = formData.cardNumber.replace(/\s/g, '')
      if (!cardNum) {
        errors.cardNumber = t('card.errors.cardNumberRequired')
      } else if (cardNum.length < 13 || cardNum.length > 19) {
        errors.cardNumber = t('card.errors.cardNumberInvalid')
      } else if (!validateLuhn(cardNum)) {
        errors.cardNumber = t('card.errors.cardNumberInvalid')
      }
      break
    }

    case 'cardholderName':
      if (!formData.cardholderName.trim()) {
        errors.cardholderName = t('card.errors.nameRequired')
      } else if (!/^[A-Z\s]+$/.test(formData.cardholderName)) {
        errors.cardholderName = t('card.errors.nameInvalid')
      }
      break

    case 'expiryDate': {
      if (!formData.expiryDate) {
        errors.expiryDate = t('card.errors.expiryRequired')
      } else if (!/^\d{2}\/\d{2}$/.test(formData.expiryDate)) {
        errors.expiryDate = t('card.errors.expiryInvalid')
      } else {
        const [month, year] = formData.expiryDate.split('/')
        const currentDate = new Date()
        const currentYear = currentDate.getFullYear() % 100
        const currentMonth = currentDate.getMonth() + 1

        if (parseInt(month) < 1 || parseInt(month) > 12) {
          errors.expiryDate = t('card.errors.expiryInvalid')
        } else if (parseInt(year) < currentYear ||
                  (parseInt(year) === currentYear && parseInt(month) < currentMonth)) {
          errors.expiryDate = t('card.errors.cardExpired')
        }
      }
      break
    }

    case 'cvv':
      if (!formData.cvv) {
        errors.cvv = t('card.errors.cvvRequired')
      } else if (formData.cvv.length < 3 || formData.cvv.length > 4) {
        errors.cvv = t('card.errors.cvvInvalid')
      }
      break
  }
}

// Luhn algorithm for card validation
const validateLuhn = (cardNumber: string): boolean => {
  let sum = 0
  let isEven = false

  for (let i = cardNumber.length - 1; i >= 0; i--) {
    let digit = parseInt(cardNumber[i])

    if (isEven) {
      digit *= 2
      if (digit > 9) {
        digit -= 9
      }
    }

    sum += digit
    isEven = !isEven
  }

  return sum % 10 === 0
}

const isFormValid = computed(() => {
  return (
    formData.cardNumber &&
    formData.cardholderName &&
    formData.expiryDate &&
    formData.cvv &&
    !Object.values(errors).some(error => error)
  )
})

const handleSubmit = async () => {
  // Validate all fields
  Object.keys(formData).forEach(field => {
    if (field !== 'saveCard') {
      validateField(field as keyof typeof formData)
    }
  })

  if (!isFormValid.value) return

  isLoading.value = true

  try {
    const userId = localStorage.getItem('userId') || 'demo-user'

    const response = await walletAPI.addCreditCard({
      userId,
      cardNumber: formData.cardNumber.replace(/\s/g, ''),
      cardholderName: formData.cardholderName,
      expiryDate: formData.expiryDate,
      cvv: formData.cvv,
      saveCard: formData.saveCard
    })

    if (response) {
      toast.success(t('card.cardAdded'))
      router.push('/pages/deposit/managepayment/managepayment')
    }
  } catch (error: any) {
    console.error('Add credit card error:', error)

    // For demo purposes, show success even if API fails
    if (error.code === 'ERR_NETWORK' || error.response?.status === 404) {
      toast.success(t('card.cardAdded'))
      router.push('/pages/deposit/managepayment/managepayment')
    } else {
      toast.error(error.response?.data?.message || t('card.addFailed'))
    }
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
/* Slide down animation */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from,
.slide-down-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style>
