<template>
  <div class="min-h-screen bg-gray-100">
    <!-- Total Balance Card -->
    <Transition name="fade-scale">
      <div v-if="!wallet.isLoading" class="bg-white px-4 py-6 text-center">
        <p class="text-gray-500 text-sm mb-2">{{ $t('balance.totalBalance') }}</p>
        <p class="text-3xl font-bold text-gray-900 mb-4">{{ $t('common.currency') }}{{ wallet.totalBalance }}</p>
        <button
          @click="$router.push('/pages/deposit/add/add')"
          class="bg-success text-white px-8 py-3 rounded-md font-medium hover:bg-success-light transition-all transform hover:scale-105 active:scale-95"
        >
          {{ $t('balance.addCash') }}
        </button>
      </div>
      <div v-else class="bg-white px-4 py-12 text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
      </div>
    </Transition>

    <!-- Balance Breakdown -->
    <Transition name="slide-fade">
      <div v-if="!wallet.isLoading" class="bg-white mt-2">
        <!-- Amount Added -->
        <div class="px-4 py-4 border-b border-gray-200 flex items-center justify-between balance-item">
          <div class="flex-1">
            <p class="text-xs text-gray-500 uppercase mb-1">{{ $t('balance.amountAdded') }}</p>
            <p class="text-lg font-semibold">{{ $t('common.currency') }}{{ wallet.amountAdded }}</p>
          </div>
          <Tooltip :text="$t('balance.tooltip.amountAdded')" />
        </div>

        <!-- Winnings -->
        <div class="px-4 py-4 border-b border-gray-200 balance-item" style="animation-delay: 0.1s">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <p class="text-xs text-gray-500 uppercase mb-1">{{ $t('balance.winnings') }}</p>
              <p class="text-lg font-semibold">{{ $t('common.currency') }}{{ wallet.winnings }}</p>
            </div>
            <Tooltip :text="$t('balance.tooltip.winnings')" />
            <button
              @click="$router.push('/pages/withdraw/index')"
              class="ml-4 bg-white border-2 border-primary text-primary px-4 py-2 rounded-md text-sm font-medium hover:bg-primary hover:text-white transition-all transform hover:scale-105 active:scale-95"
            >
              {{ $t('balance.withdrawInstantly') }}
            </button>
          </div>
        </div>

        <!-- Cash Bonus -->
        <div class="px-4 py-4 balance-item" style="animation-delay: 0.2s">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <p class="text-xs text-gray-500 uppercase mb-1">{{ $t('balance.cashBonus') }}</p>
              <p class="text-lg font-semibold">{{ $t('common.currency') }}{{ wallet.cashBonus }}</p>
            </div>
            <Tooltip :text="$t('balance.tooltip.cashBonus')" />
          </div>
        </div>

        <!-- Cash Bonus Info -->
        <div class="px-4 pb-4 balance-item" style="animation-delay: 0.3s">
          <p class="text-xs text-gray-500">
            {{ $t('balance.maxUsableCashBonus') }}
            <span class="underline text-primary cursor-pointer hover:text-primary-hover transition-colors">{{ $t('balance.knowMore') }}</span>
          </p>
        </div>

        <!-- Cash Bonus Input -->
        <div class="px-4 pb-4 balance-item" style="animation-delay: 0.4s">
          <div class="bg-gray-50 rounded-lg p-3 flex items-center gap-3">
            <div class="bg-green-100 p-2 rounded">
              <Wallet class="w-5 h-5 text-green-600" />
            </div>
            <input
              v-model="bonusCode"
              type="text"
              :placeholder="$t('balance.bonusPlaceholder')"
              class="flex-1 bg-transparent outline-none text-sm text-gray-700 placeholder-gray-400"
              @keyup.enter="handleBonusCode"
            />
            <button
              @click="handleBonusCode"
              class="text-primary text-sm font-medium hover:text-primary-hover transition-colors"
            >
              {{ $t('balance.knowMore') }}
            </button>
          </div>
        </div>
      </div>
    </Transition>

    <!-- Navigation Links -->
    <Transition name="slide-up">
      <div v-if="!wallet.isLoading" class="mt-8">
        <button
          @click="$router.push('/pages/transaction/transaction')"
          class="w-full bg-white px-4 py-4 flex items-center justify-between hover:bg-gray-50 transition-all transform hover:scale-[1.02] active:scale-[0.98] nav-item"
        >
          <span class="text-gray-900 font-medium">{{ $t('balance.myTransactions') }}</span>
          <ChevronRight class="w-5 h-5 text-gray-400" />
        </button>

        <button
          @click="$router.push('/pages/deposit/managepayment/managepayment')"
          class="w-full bg-white px-4 py-4 mt-px flex items-center justify-between hover:bg-gray-50 transition-all transform hover:scale-[1.02] active:scale-[0.98] nav-item"
          style="animation-delay: 0.1s"
        >
          <div class="text-left">
            <p class="text-gray-900 font-medium">{{ $t('balance.managePayments') }}</p>
            <p class="text-sm text-gray-500 mt-1">{{ $t('balance.managePaymentsSubtitle') }}</p>
          </div>
          <ChevronRight class="w-5 h-5 text-gray-400" />
        </button>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ChevronRight, Wallet } from 'lucide-vue-next'
import { useWalletStore } from '@/stores/wallet'
import { useToastStore } from '@/stores/toast'
import Tooltip from '@/components/Tooltip.vue'

const wallet = useWalletStore()
const toast = useToastStore()

const bonusCode = ref('')

const handleBonusCode = () => {
  if (bonusCode.value.trim()) {
    // In real app, this would validate the bonus code
    toast.info('Bonus code feature coming soon!')
    bonusCode.value = ''
  }
}

// Fetch balance on mount
onMounted(() => {
  wallet.fetchBalance()
})
</script>

<style scoped>
/* Fade scale animation */
.fade-scale-enter-active,
.fade-scale-leave-active {
  transition: all 0.3s ease;
}

.fade-scale-enter-from {
  opacity: 0;
  transform: scale(0.95);
}

.fade-scale-leave-to {
  opacity: 0;
  transform: scale(1.05);
}

/* Slide fade animation */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.4s ease;
}

.slide-fade-enter-from {
  opacity: 0;
  transform: translateY(-20px);
}

.slide-fade-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

/* Slide up animation */
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.4s ease;
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(30px);
}

.slide-up-leave-to {
  opacity: 0;
  transform: translateY(-30px);
}

/* Balance items animation */
.balance-item {
  animation: slideInLeft 0.4s ease-out forwards;
  opacity: 0;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Navigation items animation */
.nav-item {
  animation: slideInUp 0.4s ease-out forwards;
  opacity: 0;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading spinner animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
