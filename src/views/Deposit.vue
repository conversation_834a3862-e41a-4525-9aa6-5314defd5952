<template>
  <div class="min-h-screen bg-gray-100">
    <!-- Loading Overlay -->
    <Transition name="fade">
      <div
        v-if="wallet.isLoading"
        class="fixed inset-0 bg-black/50 z-50 flex items-center justify-center"
      >
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span class="text-gray-700">Processing deposit...</span>
        </div>
      </div>
    </Transition>

    <!-- Current Balance -->
    <div class="bg-white px-4 py-4 flex items-center justify-between">
      <span class="text-gray-700">{{ $t('deposit.currentBalance') }}</span>
      <span class="text-xl font-semibold">{{ $t('common.currency') }}{{ wallet.totalBalance.toFixed(2) }}</span>
    </div>

    <!-- Amount Input -->
    <div class="bg-white mt-2 px-4 py-4">
      <div
        class="flex items-center border-b border-gray-300 pb-2 px-3 rounded-t-md transition-colors"
        :class="{ 'bg-[#d1f2d7]': isFocused }"
      >
        <span class="text-gray-700 mr-2 text-xl">+</span>
        <input
          v-model.number="amount"
          type="number"
          placeholder="200"
          class="flex-1 text-xl font-medium outline-none bg-transparent"
          :class="{ 'text-red-500': validationErrors.length > 0 }"
          @input="handleAmountChange"
          @blur="handleBlur"
          @focus="isFocused = true"
        />
        <button
          @click="clearAmount"
          v-if="amount"
          class="p-1 hover:bg-gray-100 rounded-full transition-colors"
        >
          <X class="w-5 h-5 text-gray-500" />
        </button>
      </div>
      <!-- Validation Errors -->
      <Transition name="slide-down">
        <div v-if="validationErrors.length > 0" class="mt-2">
          <p v-for="error in validationErrors" :key="error" class="text-red-500 text-sm">
            {{ error }}
          </p>
        </div>
      </Transition>
    </div>

    <!-- Quick Amount Selection -->
    <div class="bg-white px-4 py-4">
      <div class="grid grid-cols-4 gap-2">
        <button
          v-for="quickAmount in [10, 50, 100, 300, 1000, 3000, 5000, 10000]"
          :key="quickAmount"
          @click="setAmount(quickAmount)"
          class="py-3 bg-gray-100 hover:bg-gray-200 rounded-md text-gray-700 font-medium transition-all hover:scale-105 active:scale-95"
        >
          {{ quickAmount }}
        </button>
      </div>
    </div>

    <!-- Payment Method -->
    <Transition name="slide-up">
      <div v-if="amount" class="mt-4">
        <h3 class="px-4 py-2 text-gray-700 font-medium">{{ $t('deposit.paymentMethod') }}</h3>
        <div class="bg-white px-4 py-2">
          <div class="grid grid-cols-2 gap-3">
            <button
              @click="wallet.selectedPaymentMethod = 'AliPay'"
              :class="[
                'relative py-4 rounded-lg border-2 transition-all transform hover:scale-105',
                wallet.selectedPaymentMethod === 'AliPay'
                  ? 'bg-primary text-white border-primary scale-105'
                  : 'bg-white text-gray-700 border-gray-200 hover:border-gray-300'
              ]"
            >
              <span class="absolute top-1 right-1 bg-orange-500 text-white text-xs px-2 py-0.5 rounded">+500</span>
              <div class="text-center">
                <p class="font-medium">AliPay</p>
              </div>
            </button>

            <button
              @click="wallet.selectedPaymentMethod = 'upi'"
              :class="[
                'relative py-4 rounded-lg border-2 transition-all transform hover:scale-105',
                wallet.selectedPaymentMethod === 'upi'
                  ? 'bg-primary text-white border-primary scale-105'
                  : 'bg-white text-gray-700 border-gray-200 hover:border-gray-300'
              ]"
            >
              <span class="absolute top-1 right-1 bg-orange-500 text-white text-xs px-2 py-0.5 rounded">+3.00%</span>
              <div class="text-center">
                <p class="font-medium">upi</p>
              </div>
            </button>
          </div>
        </div>
      </div>
    </Transition>

    <!-- Payment Channel -->
    <Transition name="slide-up">
      <div v-if="wallet.selectedPaymentMethod" class="mt-4">
        <h3 class="px-4 py-2 text-gray-700 font-medium">{{ $t('deposit.paymentChannel') }}</h3>
        <div class="bg-white px-4 py-2">
          <button
            @click="wallet.selectedPaymentChannel = 'test1009'"
            :class="[
              'w-full relative py-4 rounded-lg border-2 transition-all transform hover:scale-105',
              wallet.selectedPaymentChannel === 'test1009'
                ? 'bg-primary text-white border-primary scale-105'
                : 'bg-white text-gray-700 border-gray-200 hover:border-gray-300'
            ]"
          >
            <span class="absolute top-1 right-1 bg-orange-500 text-white text-xs px-2 py-0.5 rounded">+500</span>
            <p class="font-medium">test1009</p>
          </button>
        </div>
      </div>
    </Transition>

    <!-- Deposit Info -->
    <div class="px-4 py-2">
      <p class="text-sm text-gray-500">
        {{ $t('deposit.depositLimit', { min: 100, max: 1000000 }) }}
      </p>
    </div>

    <!-- Balance Summary -->
    <Transition name="slide-up">
      <div v-if="canDeposit" class="bg-white mt-2 px-4 py-3">
        <div class="flex justify-between mb-2">
          <span class="text-gray-700">{{ $t('deposit.cashBalance') }}</span>
          <span class="font-semibold">{{ $t('common.currency') }}{{ calculatedCashBalance.toFixed(2) }}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-700">{{ $t('deposit.cashBonus') }}</span>
          <span class="font-semibold">{{ $t('common.currency') }}{{ calculatedBonus.toFixed(2) }}</span>
        </div>
      </div>
    </Transition>

    <!-- Deposit Button -->
    <div class="px-4 py-4">
      <button
        @click="handleDeposit"
        :disabled="!canDeposit || wallet.isLoading"
        :class="[
          'w-full py-4 rounded-md font-medium transition-all transform',
          canDeposit && !wallet.isLoading
            ? 'bg-success text-white hover:bg-success-light hover:scale-105 active:scale-95'
            : 'bg-gray-300 text-gray-500 cursor-not-allowed'
        ]"
      >
        {{ $t('deposit.depositNow') }}
      </button>
    </div>

    <!-- Tips -->
    <div class="px-4 pb-4">
      <p class="text-sm text-red-500">
        {{ $t('deposit.tips', { tips: 'aaaa\nbbbbb\nccccc' }) }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { X } from 'lucide-vue-next'
import { useWalletStore } from '@/stores/wallet'
import { useTransactionsStore } from '@/stores/transactions'
import { useRouter } from 'vue-router'
import { validators } from '@/utils/validation'
import { useToastStore } from '@/stores/toast'

const wallet = useWalletStore()
const transactions = useTransactionsStore()
const router = useRouter()
const toast = useToastStore()

const amount = ref<number | string>('')
const validationErrors = ref<string[]>([])
const isFocused = ref(false)

const handleAmountChange = (e: Event) => {
  const value = (e.target as HTMLInputElement).value
  if (value === '') {
    amount.value = ''
  } else {
    amount.value = Number(value)
  }
  // Clear validation errors on input
  if (validationErrors.value.length > 0) {
    validationErrors.value = []
  }
}

const validateAmount = () => {
  const depositAmount = Number(amount.value)
  const validation = validators.depositAmount().validate(depositAmount)
  validationErrors.value = validation.errors
  return validation.isValid
}

const handleBlur = () => {
  isFocused.value = false
  validateAmount()
}

const setAmount = (value: number) => {
  amount.value = value
  validationErrors.value = []
}

const clearAmount = () => {
  amount.value = ''
  validationErrors.value = []
}

const calculatedBonus = computed(() => {
  if (!amount.value || !wallet.selectedPaymentMethod) return 0
  const baseAmount = Number(amount.value)

  if (wallet.selectedPaymentMethod === 'AliPay' && wallet.selectedPaymentChannel === 'test1009') {
    return 500 // Fixed bonus
  } else if (wallet.selectedPaymentMethod === 'upi') {
    return baseAmount * 0.03 // 3% bonus
  }
  return 0
})

const calculatedCashBalance = computed(() => {
  const baseAmount = Number(amount.value) || 0
  return baseAmount + calculatedBonus.value
})

const canDeposit = computed(() => {
  const depositAmount = Number(amount.value)
  return (
    depositAmount > 0 &&
    wallet.selectedPaymentMethod &&
    wallet.selectedPaymentChannel &&
    validationErrors.value.length === 0
  )
})

const handleDeposit = async () => {
  if (!canDeposit.value || !validateAmount()) return

  const depositAmount = Number(amount.value)
  const success = await wallet.addDeposit(depositAmount, calculatedBonus.value)

  if (success) {
    transactions.addTransaction({
      type: 'deposit',
      status: 'in-process',
      amount: depositAmount,
      paymentMethod: wallet.selectedPaymentMethod,
      timestamp: new Date().toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true }),
      orderId: `2025${Date.now()}`
    })

    router.push('/')
  }
}

// Set default selections
wallet.selectedPaymentMethod = 'AliPay'
wallet.selectedPaymentChannel = 'test1009'
</script>

<style scoped>
/* Slide up animation */
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.slide-up-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* Slide down animation */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.2s ease;
}

.slide-down-enter-from,
.slide-down-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Fade animation */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
