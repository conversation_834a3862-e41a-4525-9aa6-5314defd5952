<template>
  <div class="min-h-screen bg-gray-100">
    <!-- My Credit Cards -->
    <div class="mt-2">
      <h3 class="px-4 py-2 text-gray-700 font-medium">{{ $t('payments.myCreditCards') }}</h3>
      <div class="bg-white">
        <button
          @click="$router.push('/pages/deposit/managepayment/addcard/addcard')"
          class="w-full px-4 py-4 flex items-center justify-between hover:bg-gray-50 transition-colors"
        >
          <div class="flex items-center">
            <img :src="creditCardIcon" alt="card" class="w-10 h-8 mr-3" />
            <div class="text-left">
              <p class="text-gray-900">{{ $t('payments.addNewCreditCard') }}</p>
            </div>
          </div>
          <div class="flex items-center">
            <span class="text-gray-500 text-sm mr-2">{{ $t('common.add') }}</span>
            <ChevronRight class="w-5 h-5 text-gray-400" />
          </div>
        </button>
      </div>
    </div>

    <!-- My Bank Accounts -->
    <div class="mt-6">
      <h3 class="px-4 py-2 text-gray-700 font-medium">{{ $t('payments.myBankAccounts') }}</h3>
      <div class="bg-white">
        <button
          @click="$router.push('/pages/deposit/managepayment/addbank/addbank')"
          class="w-full px-4 py-4 flex items-center justify-between hover:bg-gray-50 transition-colors"
        >
          <div class="flex items-center">
            <img :src="visaCardIcon" alt="card" class="w-10 h-8 mr-3" />
            <div class="text-left">
              <p class="text-gray-900">{{ $t('payments.verifyNewBankAccount') }}</p>
            </div>
          </div>
          <div class="flex items-center">
            <span class="text-gray-500 text-sm mr-2">{{ $t('common.add') }}</span>
            <ChevronRight class="w-5 h-5 text-gray-400" />
          </div>
        </button>
      </div>
    </div>

    <!-- My Wallets UPI ID -->
    <div class="mt-6">
      <h3 class="px-4 py-2 text-gray-700 font-medium">{{ $t('payments.myWalletsUPI') }}</h3>
      <div class="bg-white">
        <!-- Paytm -->
        <button
          @click="selectedUPI = 'paytm'; showUPIModal = true"
          class="w-full px-4 py-4 flex items-center justify-between hover:bg-gray-50 transition-colors border-b"
        >
          <div class="flex items-center">
            <img :src="paytmIcon" alt="paytm" class="w-10 h-10 mr-3" />
            <p class="text-gray-900">Paytm</p>
          </div>
          <div class="flex items-center">
            <span class="text-gray-500 text-sm mr-2">{{ $t('common.link') }}</span>
            <ChevronRight class="w-5 h-5 text-gray-400" />
          </div>
        </button>

        <!-- PhonePe -->
        <button
          @click="selectedUPI = 'phonepe'; showUPIModal = true"
          class="w-full px-4 py-4 flex items-center justify-between hover:bg-gray-50 transition-colors border-b"
        >
          <div class="flex items-center">
            <img :src="phonepeIcon" alt="phonepe" class="w-10 h-10 mr-3" />
            <p class="text-gray-900">Phonepe</p>
          </div>
          <div class="flex items-center">
            <span class="text-gray-500 text-sm mr-2">{{ $t('common.link') }}</span>
            <ChevronRight class="w-5 h-5 text-gray-400" />
          </div>
        </button>

        <!-- GPay -->
        <button
          @click="selectedUPI = 'gpay'; showUPIModal = true"
          class="w-full px-4 py-4 flex items-center justify-between hover:bg-gray-50 transition-colors border-b"
        >
          <div class="flex items-center">
            <img :src="gpayIcon" alt="gpay" class="w-10 h-10 mr-3" />
            <p class="text-gray-900">Gpay</p>
          </div>
          <div class="flex items-center">
            <span class="text-gray-500 text-sm mr-2">{{ $t('common.link') }}</span>
            <ChevronRight class="w-5 h-5 text-gray-400" />
          </div>
        </button>

        <!-- Other UPI -->
        <button
          @click="selectedUPI = 'other'; showUPIModal = true"
          class="w-full px-4 py-4 flex items-center justify-between hover:bg-gray-50 transition-colors"
        >
          <div class="flex items-center">
            <img :src="upiIcon" alt="upi" class="w-10 h-10 mr-3" />
            <p class="text-gray-900">{{ $t('payments.otherUPIID') }}</p>
          </div>
          <div class="flex items-center">
            <span class="text-gray-500 text-sm mr-2">{{ $t('common.link') }}</span>
            <ChevronRight class="w-5 h-5 text-gray-400" />
          </div>
        </button>
      </div>
    </div>

    <!-- UPI Modal -->
    <Transition name="slide-up">
      <div
        v-if="showUPIModal"
        class="fixed inset-0 bg-black/50 z-50 flex items-end"
        @click="showUPIModal = false"
      >
        <div
          class="bg-white rounded-t-2xl w-full px-4 py-6"
          @click.stop
        >
          <h3 class="text-center text-gray-700 mb-6">{{ $t('payments.enterValidUPI') }}</h3>

          <div class="mb-4">
            <input
              v-model="upiId"
              type="text"
              :placeholder="$t('payments.upiPlaceholder')"
              class="w-full px-4 py-3 border-b-2 border-gray-300 focus:border-primary outline-none text-lg"
            />
          </div>

          <p class="text-center text-sm text-gray-500 mb-6">
            {{ $t('payments.upiNote') }}
          </p>

          <button
            @click="handleUPISubmit"
            :disabled="!upiId"
            :class="[
              'w-full py-3 rounded-md font-medium transition-all',
              upiId
                ? 'bg-primary text-white hover:bg-primary-hover'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            ]"
          >
            {{ $t('payments.proceed') }}
          </button>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ChevronRight } from 'lucide-vue-next'
import { useToastStore } from '@/stores/toast'
import creditCardIcon from '@/assets/images/credit-card.png'
import visaCardIcon from '@/assets/images/visa-card.png'
import paytmIcon from '@/assets/images/paytm.png'
import phonepeIcon from '@/assets/images/phonepe.png'
import gpayIcon from '@/assets/images/gpay.png'
import upiIcon from '@/assets/images/upi.png'

const toast = useToastStore()
const showAddBankModal = ref(false)
const showUPIModal = ref(false)
const selectedUPI = ref('')
const upiId = ref('')

const handleUPISubmit = () => {
  if (!upiId.value) return

  // Here you would normally save the UPI ID
  toast.success(`${selectedUPI.value} UPI ID linked successfully`)

  // Reset and close
  upiId.value = ''
  showUPIModal.value = false
}
</script>
