<template>
  <div class="min-h-screen bg-gray-100">
    <!-- Tabs -->
    <div class="bg-white sticky top-14 z-10">
      <div class="flex">
        <button
          v-for="tab in tabs"
          :key="tab.key"
          @click="transactions.setActiveTab(tab.key as TabType)"
          :class="[
            'flex-1 py-3 text-center font-medium transition-all relative',
            transactions.activeTab === tab.key
              ? 'text-primary'
              : 'text-gray-500'
          ]"
        >
          {{ $t(`transactions.${tab.key}`) }}
          <div
            v-if="transactions.activeTab === tab.key"
            class="absolute bottom-0 left-0 right-0 h-0.5 bg-primary"
          />
        </button>
      </div>
    </div>

    <!-- Filters -->
    <div v-if="currentFilters.length > 0" class="bg-white px-4 py-3 flex items-center justify-between border-t">
      <div class="flex gap-2 flex-1">
        <button
          v-for="filter in currentFilters"
          :key="filter.key"
          @click="transactions.activeFilter = filter.key as FilterType"
          :class="[
            'px-4 py-2 rounded-full text-sm font-medium transition-all',
            transactions.activeFilter === filter.key
              ? 'bg-primary text-white'
              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
          ]"
        >
          {{ $t(`transactions.${filter.key.replace(/-/g, '').toLowerCase()}`) }}
        </button>
      </div>
      <button @click="showFilterMenu = !showFilterMenu" class="p-2 hover:bg-gray-100 rounded-lg transition-colors">
        <Filter class="w-5 h-5 text-gray-600" />
      </button>
    </div>

    <!-- Info Message -->
    <div v-if="infoMessage" class="bg-red-50 px-4 py-3 text-center">
      <p class="text-sm text-red-600">{{ infoMessage }}</p>
    </div>

    <!-- Transaction List -->
    <div class="mt-2">
      <template v-if="filteredTransactions.length > 0">
        <!-- Date Header -->
        <div class="bg-gray-50 px-4 py-2">
          <p class="text-sm text-gray-600">07/30/2025</p>
        </div>

        <!-- Transactions -->
        <div class="bg-white">
          <div
            v-for="transaction in filteredTransactions"
            :key="transaction.id"
            class="px-4 py-4 border-b border-gray-100 last:border-b-0"
          >
            <div class="flex items-start">
              <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                <Clock class="w-5 h-5 text-blue-600" />
              </div>

              <div class="flex-1">
                <div class="flex items-start justify-between mb-1">
                  <div>
                    <p class="text-gray-900 font-medium">{{ transaction.orderId }}</p>
                    <p class="text-sm text-gray-600">{{ transaction.paymentMethod }}</p>
                    <p class="text-sm text-gray-600">{{ getStatusText(transaction.status) }}</p>
                  </div>
                  <div class="text-right">
                    <button
                      @click="copyOrderId(transaction.orderId)"
                      class="text-sm text-gray-500 border border-gray-300 px-2 py-0.5 rounded hover:bg-gray-50"
                    >
                      {{ $t('common.copy') }}
                    </button>
                    <p class="text-sm text-gray-600 mt-1">{{ transaction.timestamp }}</p>
                    <p class="font-semibold">{{ $t('common.currency') }}{{ transaction.amount.toFixed(2) }}</p>
                  </div>
                </div>

                <button
                  @click="handleChatClick(transaction.orderId)"
                  class="text-sm text-gray-500 mt-2 hover:text-primary transition-colors flex items-center"
                >
                  {{ $t('transactions.needHelp') }}
                  <img :src="chatIcon" alt="chat" class="w-5 h-5 ml-1" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- Empty State -->
      <template v-else>
        <div class="bg-white mt-2 px-4 py-16 text-center">
          <img :src="logoIcon" alt="logo" class="w-24 h-24 mx-auto mb-4" />
          <p class="text-gray-600 text-lg mb-2">You Only Need One Games APP.</p>
          <p class="text-gray-500">{{ $t('transactions.noTransactions') }}</p>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { Clock, Filter } from 'lucide-vue-next'
import { useTransactionsStore, type TabType, type FilterType } from '@/stores/transactions'
import { useI18n } from 'vue-i18n'
import { useToastStore } from '@/stores/toast'
import logoIcon from '@/assets/images/logo.png'

const { t } = useI18n()
const transactions = useTransactionsStore()
const toast = useToastStore()

const showFilterMenu = ref(false)

const tabs = [
  { key: 'deposits', label: 'Deposits' },
  { key: 'withdrawals', label: 'Withdrawals' },
  { key: 'bet', label: 'Bet' },
  { key: 'bonus', label: 'Bonus' }
]

// Different filters for different tabs
const filtersByTab = {
  deposits: [
    { key: 'success', label: 'Success' },
    { key: 'in-process', label: 'In-process' },
    { key: 'failed', label: 'Failed' }
  ],
  withdrawals: [
    { key: 'success', label: 'Success' },
    { key: 'in-process', label: 'In-process' },
    { key: 'refund', label: 'Refund' },
    { key: 'failed', label: 'Failed' }
  ],
  bet: [
    { key: 'win', label: 'Win' },
    { key: 'loss', label: 'Loss' }
  ],
  bonus: [
    { key: 'transferin', label: 'Transfer in' },
    { key: 'transferout', label: 'Transfer Out' }
  ]
}

const currentFilters = computed(() => {
  return filtersByTab[transactions.activeTab] || []
})

const infoMessage = computed(() => {
  switch (transactions.activeTab) {
    case 'deposits':
      return t('transactions.depositCreditedInfo')
    case 'withdrawals':
      return t('transactions.withdrawProcessedInfo')
    case 'bet':
      return t('transactions.bettingRecordsInfo')
    case 'bonus':
      return t('transactions.bonusRecordsInfo')
    default:
      return ''
  }
})

const filteredTransactions = computed(() => {
  return transactions.getFilteredTransactions()
})

const getStatusText = (status: string) => {
  // Handle hyphenated status names by removing hyphens and converting to lowercase
  const translationKey = status.replace(/-/g, '').toLowerCase()
  return t(`transactions.${translationKey}`)
}

const copyOrderId = (orderId: string) => {
  navigator.clipboard.writeText(orderId)
  toast.success('Order ID copied!')
}

const handleChatClick = (orderId: string) => {
  toast.info(`Customer support for order ${orderId} coming soon!`)
}

// Use the chat icon URL directly
const chatIcon = 'https://ext.same-assets.com/69523784/519222773.png'
</script>
