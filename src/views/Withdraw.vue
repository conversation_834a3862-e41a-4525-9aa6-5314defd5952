<template>
  <div class="min-h-screen bg-gray-100">
    <!-- Loading Overlay -->
    <Transition name="fade">
      <div
        v-if="wallet.isLoading"
        class="fixed inset-0 bg-black/50 z-50 flex items-center justify-center"
      >
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span class="text-gray-700">Processing withdrawal...</span>
        </div>
      </div>
    </Transition>

    <!-- Withdrawable Balance -->
    <div class="bg-white px-4 py-4 flex items-center justify-between">
      <span class="text-gray-700">{{ $t('withdraw.withdrawableBalance') }}</span>
      <span class="text-xl font-semibold">{{ $t('common.currency') }}{{ wallet.withdrawableBalance }}</span>
    </div>

    <!-- Bank Account Section -->
    <div class="bg-white mt-2">
      <button
        @click="showAddBankModal = true"
        class="w-full px-4 py-4 flex items-center justify-between hover:bg-gray-50 transition-all transform hover:scale-[1.02] active:scale-[0.98]"
      >
        <div class="flex items-center">
          <img :src="visaCardIcon" alt="card" class="w-10 h-8 mr-3" />
          <div class="text-left">
            <p class="text-gray-900 font-medium">{{ $t('withdraw.addNewBankAccount') }}</p>
            <p class="text-sm text-gray-500">{{ $t('withdraw.addNewBankAccountDesc') }}</p>
          </div>
        </div>
        <ChevronRight class="w-5 h-5 text-gray-400" />
      </button>
    </div>

    <!-- Amount Section -->
    <div class="fixed bottom-0 left-0 right-0 bg-white shadow-lg">
      <div class="px-4 pt-4">
        <p class="text-gray-700 mb-2">{{ $t('withdraw.amount') }}</p>
        <div class="flex items-center border-b-2 border-gray-300 pb-2">
          <span class="text-xl font-medium mr-1">{{ $t('common.currency') }}</span>
          <input
            v-model.number="amount"
            type="number"
            placeholder="0"
            class="flex-1 text-xl font-medium outline-none bg-transparent"
            :class="{ 'text-red-500': validationErrors.length > 0 }"
            @input="handleAmountChange"
            @blur="validateAmount"
          />
        </div>

        <!-- Validation Errors -->
        <Transition name="slide-down">
          <div v-if="validationErrors.length > 0" class="mt-2">
            <p v-for="error in validationErrors" :key="error" class="text-red-500 text-sm">
              {{ error }}
            </p>
          </div>
        </Transition>

        <p class="text-sm text-gray-500 mt-2">
          {{ $t('withdraw.withdrawLimit', { min: 0, max: wallet.withdrawableBalance }) }}
        </p>
      </div>

      <div class="px-4 py-4">
        <button
          @click="handleWithdraw"
          :disabled="!canWithdraw || wallet.isLoading"
          :class="[
            'w-full py-4 rounded-md font-medium transition-all transform',
            canWithdraw && !wallet.isLoading
              ? 'bg-success text-white hover:bg-success-light hover:scale-105 active:scale-95'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          ]"
        >
          {{ $t('withdraw.withdrawNow') }}
        </button>
      </div>

      <div class="px-4 pb-4">
        <p class="text-sm text-red-500">bbbbdddd<br>awerqwerqwer</p>
      </div>
    </div>

    <!-- Add Bank Modal -->
    <Transition name="modal">
      <div
        v-if="showAddBankModal"
        class="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
        @click="showAddBankModal = false"
      >
        <div
          class="bg-white rounded-lg p-6 max-w-sm w-full transform transition-all"
          :class="showAddBankModal ? 'scale-100' : 'scale-95'"
          @click.stop
        >
          <h3 class="text-lg font-semibold mb-4">{{ $t('withdraw.addNewBankAccount') }}</h3>
          <p class="text-gray-600 mb-4">{{ $t('withdraw.addNewBankAccountDesc') }}</p>
          <button
            @click="showAddBankModal = false"
            class="w-full py-3 bg-primary text-white rounded-md hover:bg-primary-hover transition-colors"
          >
            {{ $t('common.ok') }}
          </button>
        </div>
      </div>
    </Transition>

    <!-- Error Toast -->
    <Transition name="slide-up">
      <div
        v-if="showError"
        class="fixed top-16 left-4 right-4 bg-success text-white px-4 py-3 rounded-md shadow-lg z-50"
      >
        无法连接到服务器~
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ChevronRight } from 'lucide-vue-next'
import { useWalletStore } from '@/stores/wallet'
import { useTransactionsStore } from '@/stores/transactions'
import { useRouter } from 'vue-router'
import { validators } from '@/utils/validation'
import { useToastStore } from '@/stores/toast'
import visaCardIcon from '@/assets/images/visa-card.png'

const wallet = useWalletStore()
const transactions = useTransactionsStore()
const router = useRouter()
const toast = useToastStore()

const amount = ref<number | string>('')
const showAddBankModal = ref(false)
const showError = ref(false)
const validationErrors = ref<string[]>([])

const handleAmountChange = () => {
  // Clear validation errors on input
  if (validationErrors.value.length > 0) {
    validationErrors.value = []
  }
}

const validateAmount = () => {
  const withdrawAmount = Number(amount.value)
  const validation = validators.withdrawAmount(wallet.withdrawableBalance).validate(withdrawAmount)
  validationErrors.value = validation.errors
  return validation.isValid
}

const canWithdraw = computed(() => {
  const withdrawAmount = Number(amount.value)
  return (
    withdrawAmount > 0 &&
    withdrawAmount <= wallet.withdrawableBalance &&
    validationErrors.value.length === 0
  )
})

const handleWithdraw = async () => {
  if (!canWithdraw.value || !validateAmount()) return

  const withdrawAmount = Number(amount.value)
  const success = await wallet.withdraw(withdrawAmount)

  if (success) {
    transactions.addTransaction({
      type: 'withdrawal',
      status: 'in-process',
      amount: withdrawAmount,
      paymentMethod: 'Bank Transfer',
      timestamp: new Date().toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true }),
      orderId: `2025${Date.now()}`
    })

    router.push('/')
  }
}

// Show error toast on mount (simulating API error)
onMounted(() => {
  setTimeout(() => {
    showError.value = true
    setTimeout(() => {
      showError.value = false
    }, 3000)
  }, 500)
})
</script>

<style scoped>
/* Modal animation */
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

.modal-enter-active .bg-white,
.modal-leave-active .bg-white {
  transition: transform 0.3s ease;
}

.modal-enter-from .bg-white {
  transform: scale(0.95);
}

.modal-leave-to .bg-white {
  transform: scale(0.95);
}

/* Slide down animation */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.2s ease;
}

.slide-down-enter-from,
.slide-down-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Slide up animation */
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from,
.slide-up-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

/* Fade animation */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
