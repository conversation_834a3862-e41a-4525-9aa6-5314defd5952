/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#0a7f75',
          hover: '#096b62',
          light: '#0e9485'
        },
        success: {
          DEFAULT: '#10b981',
          light: '#34d399'
        },
        warning: {
          DEFAULT: '#f59e0b',
          light: '#fbbf24'
        },
        danger: {
          DEFAULT: '#ef4444',
          light: '#f87171'
        }
      },
      fontFamily: {
        sans: ['-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif']
      }
    },
  },
  plugins: [],
}
